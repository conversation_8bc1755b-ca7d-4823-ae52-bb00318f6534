import React from "react";

import Image from "next/image";
import { InvoiceDirectDownloadForm } from "~/app/invoiceDownload/InvoiceDirectDownloadForm";
import Link from "next/link";

const Page = () => {
  return (
    <body className={""}>
      <InvoiceDirectDownloadForm />
      <div className="mt-8">
        {/* Deinen Erklärungstext hier einfügen */}
        <p className="text-gray-700">
          Sie können bei Ad-hoc-Zahlungen Ihre E-Mail-Adresse hinterlegen.
          Alle Rechnungen, für die Sie dies getan haben, lassen sich hier bequem einsehen.
          Zusätzlich haben Sie die Möglichkeit, fehlende Rechnungen nachträglich zu ergänzen.
        </p>
        {/* Button zur Batch-Seite */}
        <Link
          href="/invoiceDownloadBatch"
          className="mt-4 inline-flex items-center gap-2 rounded-none bg-elm-700 px-4 py-2 text-white hover:bg-elm-800"
        >
          Rechnungen einsehen
        </Link>
      </div>
    </body>

  );
};

export default Page;
