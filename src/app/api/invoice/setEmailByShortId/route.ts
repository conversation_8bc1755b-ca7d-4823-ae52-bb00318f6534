import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "~/server/db";
import { cookies } from "next/headers";

export const revalidate = 0;

const Schema = z.object({
  shortCdrId: z.string().length(12, "shortCdrId must be 12 characters"),
  email: z.string().email("Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein"),
});

export async function POST(req: NextRequest) {
  try {
    const parsed = Schema.safeParse(await req.json());

    if (!parsed.success) {
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    const shortId = parsed.data.shortCdrId.replace(/X+$/, "");
    const email = parsed.data.email.trim().toLowerCase();

    // 1) Direkt über authorization_reference suchen
    let invoice = await prisma.invoice.findFirst({
      where: {
        authorization_reference: {
          contains: shortId,
          mode: "insensitive",
        },
      },
    });

    // 2) Fallback: über CDR suchen und dann die passende Rechnung per authorization_reference holen
    if (!invoice) {
      const cdr = await prisma.cdr.findFirst({
        where: {
          OR: [
            { id: { contains: shortId, mode: "insensitive" } },
            {
              authorization_reference: {
                contains: shortId,
                mode: "insensitive",
              },
            },
          ],
        },
      });

      if (cdr?.authorization_reference) {
        invoice = await prisma.invoice.findFirst({
          where: { authorization_reference: cdr.authorization_reference },
        });
      }
    }

    if (!invoice) {
      return NextResponse.json(
        { error: "No matching invoice" },
        { status: 404 }
      );
    }

    await prisma.invoice.update({
      where: { id: invoice.id },
      data: { mailToSend: email },
    });

    return NextResponse.json({ ok: true }, { status: 200 });
  } catch (e) {
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
