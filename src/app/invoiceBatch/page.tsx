import InvoiceBatchView from "../invoiceDownloadBatch/InvoiceBatchView";

import AdhocEmailBackfillForm from "../invoiceDownloadBatch/AdhocEmailBackfillForm";
import AdhocEmailBackfillByDateAmountForm from "../invoiceDownloadBatch/AdhocEmailBackfillByDateAmountForm";
export default function Page({ searchParams }: { searchParams: { token?: string; id?: string } }) {
  const { token, id } = searchParams;
  return (
    <body className="bg-stone-200 min-h-screen">
    <div className="p-4">
      <h1 className="text-2xl font-bold text-black mb-2">Rechnungen-Übersicht</h1>
      <InvoiceBatchView token={token} id={id} />

      <hr className="my-6 border-gray-300" />
      <h2 className="text-xl font-bold text-black mb-2">E-Mail nachtragen</h2>

      <div className="space-y-3">
        <details className="group border rounded-md bg-white open:shadow">
          <summary className="cursor-pointer list-none px-4 py-3 flex items-center justify-between">
            <span className="font-semibold text-black">Per Transaktions-ID</span>
            <span className="ml-4 text-gray-500 transition-transform group-open:rotate-180">▾</span>
          </summary>
          <div className="px-4 pb-4 text-gray-700">
            <p className="mb-4">Tragen Sie zu einer Transaktion nachträglich Ihre E-Mail-Adresse ein.</p>
            <AdhocEmailBackfillForm />
          </div>
        </details>

        <details className="group border rounded-md bg-white open:shadow">
          <summary className="cursor-pointer list-none px-4 py-3 flex items-center justify-between">
            <span className="font-semibold text-black">Per Datum & Betrag</span>
            <span className="ml-4 text-gray-500 transition-transform group-open:rotate-180">▾</span>
          </summary>
          <div className="px-4 pb-4 text-gray-700">
            <p className="mb-4">Falls die Transaktions-ID nicht verfügbar ist, können Sie alternativ über Datum und exakten Betrag  die E-Mail hinterlegen.</p>
            <AdhocEmailBackfillByDateAmountForm />
          </div>
        </details>
      </div>

    </div>
    </body>
  );
}
