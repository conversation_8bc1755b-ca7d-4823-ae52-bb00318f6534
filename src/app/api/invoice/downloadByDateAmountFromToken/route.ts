import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "~/server/db";
import { InvoiceManager } from "~/utils/invoice";
import { cookies } from "next/headers";

export const revalidate = 0;

const Schema = z.object({
  dateStr: z.string().length(10, "dateStr must be YYYY-MM-DD"),
  amount: z.coerce.number(), // sum_gross
});

export async function POST(req: NextRequest) {
  try {
    const parsed = Schema.safeParse(await req.json());
    if (!parsed.success) {
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    const { dateStr, amount } = parsed.data;
    
    // Get email from cookie (Magic Link session)
    const cookie = cookies().get("invoice_batch_auth");
    const mlId = cookie?.value;
    if (!mlId) {
      return new Response("Nicht autorisiert", { status: 401 });
    }

    const ml = await prisma.invoiceBatchMagicLink.findUnique({
      where: { id: mlId },
    });
    if (!ml || ml.expiresAt < new Date()) {
      return new Response("Link ungültig oder abgelaufen", { status: 401 });
    }
    
    const email = ml.email.trim().toLowerCase();

    // Suche Rechnungen mit exakt diesem Betrag, Datum und E-Mail
    const rawInvoices = await prisma.invoice.findRaw({
      filter: {
        sum_gross: amount,
        mailToSend: { $regex: new RegExp(`^${email}$`, "i") },
        $expr: {
          $eq: [
            { $dateToString: { format: "%Y-%m-%d", date: "$invoice_date" } },
            dateStr,
          ],
        },
      },
    });

    if (!rawInvoices || rawInvoices.length === 0) {
      return new Response("Keine passende Rechnung gefunden", { status: 404 });
    }
    if (rawInvoices && Array.isArray(rawInvoices) && rawInvoices?.length > 1) {
      return new Response("Mehrere Rechnungen gefunden - bitte präzisieren Sie Ihre Angaben", { status: 409 });
    }

    const rawInvoice = rawInvoices[0];
    if (
      !rawInvoice ||
      typeof rawInvoice !== "object" ||
      !("_id" in rawInvoice)
    ) {
      return new Response("Ungültige Rechnungsdaten", { status: 500 });
    }

    const mongoId = rawInvoice._id;
    if (!mongoId || typeof mongoId !== "object" || !("$oid" in mongoId)) {
      return new Response("Ungültige Rechnungs-ID", { status: 500 });
    }

    const invoiceId = mongoId.$oid as string;

    // Rechnung aus der Datenbank holen
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
    });

    if (!invoice) {
      return new Response("Rechnung nicht gefunden", { status: 404 });
    }

    // PDF generieren
    const invoiceManager = new InvoiceManager();
    const pdfBuffer = await invoiceManager.generateInvoicePdf(invoice);

    // PDF als Download zurückgeben
    return new Response(pdfBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="Rechnung_${dateStr}_${amount.toFixed(2)}.pdf"`,
      },
    });
  } catch (e) {
    console.error("Error in downloadByDateAmountFromToken:", e);
    return new Response("Serverfehler", { status: 500 });
  }
}
