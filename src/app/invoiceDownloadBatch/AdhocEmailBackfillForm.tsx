"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSync } from "react-icons/fa";
import { useRouter } from "next/navigation";

interface FormValues {
  shortCdrId: string;
}

export default function AdhocEmailBackfillForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isSubmitSuccessful },
    reset,
  } = useForm<FormValues>();
  const router = useRouter();

  const [apiError, setApiError] = useState<string>("");
  const [success, setSuccess] = useState<boolean>(false);

  const onSubmit = async (data: FormValues) => {
    setApiError("");
    setSuccess(false);
    try {
      const res = await fetch("/api/invoice/setEmailByShortId", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          shortCdrId: data.shortCdrId,
        }),
      });
      if (res.ok) {
        setSuccess(true);
        reset({ shortCdrId: "" });
        window.location.reload();
      } else {
        setApiError("Aktualisierung fehlgeschlagen");
      }
    } catch (e) {
      setApiError("Serverfehler");
    }
  };

  return (
    <div className="w-full">
      {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="flex flex-col md:flex-row md:items-end md:gap-4">
          <div className="min-w-0 md:flex-1">
            <label
              className="mb-2 block text-xl font-bold text-black"
              htmlFor="shortCdrId"
            >
              Transaktions-ID
            </label>
            <input
              id="shortCdrId"
              placeholder="z.B. F123646FA1EX"
              className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-700 focus:border-elm-600 focus:outline-none"
              {...register("shortCdrId", {
                required: "Dieses Feld ist erforderlich",
                minLength: {
                  value: 12,
                  message: "Bitte genau 12 Zeichen eingeben (zu wenig)",
                },
                maxLength: {
                  value: 12,
                  message: "Bitte genau 12 Zeichen eingeben (zu viele)",
                },
              })}
            />
            {errors.shortCdrId && (
              <p className="mt-1 text-sm text-red-600">
                {errors.shortCdrId.message}
              </p>
            )}
          </div>



          <button
            className="mt-2 inline-flex shrink-0 items-center gap-2 self-start rounded-md bg-elm-700 px-4 py-2 text-white transition-colors hover:bg-elm-800 md:mt-0"
            type="submit"
            disabled={isSubmitting}
          >
            Abrufen{" "}
            {isSubmitting ? <FaSpinner className="animate-spin" /> : <FaSync />}
          </button>
        </div>
      </form>

      {/* Rückmeldung */}
      {isSubmitSuccessful && (
        <div className="mt-4">
          {success ? (
            <p className="text-green-700">
              E-Mail wurde gespeichert (falls eine passende Rechnung gefunden
              wurde).
            </p>
          ) : (
            <p className="text-red-600">
              {apiError || "Aktualisierung fehlgeschlagen"}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
