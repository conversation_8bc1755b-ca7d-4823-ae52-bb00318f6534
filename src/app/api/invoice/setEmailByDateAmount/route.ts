import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "~/server/db";
import { DateTime } from "luxon";

export const revalidate = 0;

const Schema = z.object({
  dateStr: z.string().length(10, "dateStr must be YYYY-MM-DD"),
  amount: z.coerce.number(), // sum_gross
  email: z.string().email("Bitte geben Sie eine gültige E-Mail-Adresse ein"),
});

export async function POST(req: NextRequest) {
  try {
    const parsed = Schema.safeParse(await req.json());
    if (!parsed.success) {
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    const { dateStr, amount } = parsed.data;
    const email = parsed.data.email.trim().toLowerCase();

    // Suche Rechnungen mit exakt diesem Betrag und Datum (nur Tag-Vergleich)
    const rawInvoices = await prisma.invoice.findRaw({
      filter: {
        sum_gross: amount,
        $expr: {
          $eq: [
            { $dateToString: { format: "%Y-%m-%d", date: "$invoice_date" } },
            dateStr,
          ],
        },
      },
    });

    if (!rawInvoices || rawInvoices.length === 0) {
      return NextResponse.json(
        { error: "No matching invoice" },
        { status: 404 }
      );
    }
    if (rawInvoices && Array.isArray(rawInvoices) && rawInvoices?.length > 1) {
      return NextResponse.json(
        { error: "More than one invoice matches date and amount" },
        { status: 409 }
      );
    }

    const rawInvoice = rawInvoices[0];
    if (
      !rawInvoice ||
      typeof rawInvoice !== "object" ||
      !("_id" in rawInvoice)
    ) {
      return NextResponse.json(
        { error: "Invalid invoice data" },
        { status: 500 }
      );
    }

    const mongoId = rawInvoice._id;
    if (!mongoId || typeof mongoId !== "object" || !("$oid" in mongoId)) {
      return NextResponse.json(
        { error: "Invalid invoice ID" },
        { status: 500 }
      );
    }

    const invoiceId = mongoId.$oid as string;

    await prisma.invoice.update({
      where: { id: invoiceId },
      data: { mailToSend: email },
    });

    return NextResponse.json({ ok: true }, { status: 200 });
  } catch (e) {
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
