"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { FaSpin<PERSON>, FaSync } from "react-icons/fa";

interface FormValues {
  dateStr: string; // YYYY-MM-DD
  amount: string; // entered as string, converted to number
}

export default function AdhocEmailBackfillByDateAmountFormFromToken() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting, isSubmitSuccessful },
    reset,
  } = useForm<FormValues>();

  const [apiError, setApiError] = useState<string>("");
  const [success, setSuccess] = useState<boolean>(false);

  const onSubmit = async (data: FormValues) => {
    setApiError("");
    setSuccess(false);
    try {
      const amount = parseFloat(data.amount);
      const res = await fetch("/api/invoice/setEmailByDateAmountFromToken", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          dateStr: data.dateStr,
          amount,
        }),
      });
      if (res.ok) {
        setSuccess(true);
        reset({ dateStr: "", amount: "" });
        window.location.reload();
      } else {
        setApiError("Aktualisierung fehlgeschlagen");
      }
    } catch (e) {
      setApiError("Serverfehler");
    }
  };

  return (
    <div className="w-full">
      {/* eslint-disable-next-line @typescript-eslint/no-misused-promises */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="flex flex-col md:flex-row md:items-end md:gap-4">
          <div className="min-w-0 md:flex-1">
            <label
              className="mb-2 block text-xl font-bold text-black"
              htmlFor="dateStr"
            >
              Datum
            </label>
            <input
              id="dateStr"
              type="date"
              className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-700 focus:border-elm-600 focus:outline-none"
              {...register("dateStr", {
                required: "Dieses Feld ist erforderlich",
              })}
            />
            {errors.dateStr && (
              <p className="mt-1 text-sm text-red-600">
                {errors.dateStr.message}
              </p>
            )}
          </div>

          <div className="min-w-0 md:flex-1">
            <label
              className="mb-2 block text-xl font-bold text-black"
              htmlFor="amount"
            >
              Betrag
            </label>
            <input
              id="amount"
              type="number"
              inputMode="decimal"
              step="0.01"
              placeholder="z.B. 12.34"
              className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm text-gray-700 focus:border-elm-600 focus:outline-none"
              {...register("amount", {
                required: "Dieses Feld ist erforderlich",
              })}
            />
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600">
                {errors.amount.message}
              </p>
            )}
          </div>

          <button
            className="mt-2 inline-flex shrink-0 items-center gap-2 self-start rounded-md bg-elm-700 px-4 py-2 text-white transition-colors hover:bg-elm-800 md:mt-0"
            type="submit"
            disabled={isSubmitting}
          >
            Abrufen{" "}
            {isSubmitting ? <FaSpinner className="animate-spin" /> : <FaSync />}
          </button>
        </div>
      </form>

      {/* Rückmeldung */}
      {isSubmitSuccessful && (
        <div className="mt-4">
          {success ? (
            <p className="text-green-700">
              E-Mail wurde gespeichert (falls eine passende Rechnung gefunden
              wurde).
            </p>
          ) : (
            <p className="text-red-600">
              {apiError || "Aktualisierung fehlgeschlagen"}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
